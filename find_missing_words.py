#!/usr/bin/env python3
import sqlite3
import os
import re

def get_w_series_words_from_db():
    """从数据库获取所有 w 系列的单词"""
    conn = sqlite3.connect('instance/words.db')
    cursor = conn.cursor()
    
    # 查询所有 section 以 w 开头的单词
    cursor.execute("SELECT english_word, section FROM word WHERE section LIKE 'w%' ORDER BY section, english_word")
    words = cursor.fetchall()
    conn.close()
    
    return words

def get_existing_memory_files():
    """获取现有的助记文件列表"""
    memory_dir = 'static/cache/memory_help_new'
    if not os.path.exists(memory_dir):
        return set()
    
    files = os.listdir(memory_dir)
    # 移除 .md 扩展名，获取单词名
    words = set()
    for file in files:
        if file.endswith('.md'):
            word = file[:-3]  # 移除 .md
            words.add(word)
    
    return words

def normalize_word(word):
    """标准化单词名，用于文件名比较"""
    # 替换空格为下划线，转换为小写
    return word.replace(' ', '_').lower()

def main():
    # 获取数据库中的 w 系列单词
    db_words = get_w_series_words_from_db()
    print(f"数据库中找到 {len(db_words)} 个 w 系列单词")
    
    # 获取现有的助记文件
    existing_files = get_existing_memory_files()
    print(f"现有助记文件 {len(existing_files)} 个")
    
    # 找出缺失的单词
    missing_words = []
    
    for word, section in db_words:
        # 标准化单词名用于比较
        normalized_word = normalize_word(word)
        
        # 检查是否存在对应的助记文件
        if normalized_word not in existing_files:
            missing_words.append((word, section))
    
    print(f"\n找到 {len(missing_words)} 个缺失的助记文件:")
    print("=" * 50)
    
    # 按 section 分组显示
    current_section = None
    for word, section in missing_words:
        if section != current_section:
            print(f"\n[{section}]")
            current_section = section
        print(f"  - {word}")
    
    return missing_words

if __name__ == "__main__":
    missing_words = main()
